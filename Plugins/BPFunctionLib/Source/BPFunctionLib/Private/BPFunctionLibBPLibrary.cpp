// Copyright Epic Games, Inc. All Rights Reserved.

#include "BPFunctionLibBPLibrary.h"
#include "YunnToPolygon.h"
#include "BPFunctionLib.h"
#include "ImageUtils.h"
#include "Engine/Texture2D.h"
#include "Misc/SecureHash.h"
#include "SocketSubsystem.h"
#include "Components/PrimitiveComponent.h"
#include "Engine/Level.h"
#include <windows.h>
#include <iostream>
#include <string.h>
#include "Misc/FileHelper.h"
#include <HAL/PlatformFileManager.h>
#include <JsonObjectConverter.h>
#include "Iphlpapi.h"
#include <GameFramework/SaveGame.h>

UBPFunctionLibBPLibrary::UBPFunctionLibBPLibrary(const FObjectInitializer& ObjectInitializer)
: Super(ObjectInitializer)
{

}

float UBPFunctionLibBPLibrary::BPFunctionLibSampleFunction(float Param)
{
	return -1;
}

FButtonStyle UBPFunctionLibBPLibrary::LoadImageAndCreateButtonStyle(FString LoadPath)
{

	UTexture2D* LoadedTexture = FImageUtils::ImportFileAsTexture2D(LoadPath);
	FButtonStyle BtnStyle = FButtonStyle();
	if (!LoadedTexture)
	{
		UE_LOG(LogTemp, Log, TEXT("FilePath:%s is not exit"), *LoadPath);
		return BtnStyle;
	}

	FVector2D ImageSize(LoadedTexture->GetSizeX(), LoadedTexture->GetSizeY());
	FSlateBrush Brush;
	Brush.SetResourceObject(LoadedTexture);
	Brush.SetImageSize(ImageSize);


	BtnStyle.SetNormal(Brush);
	BtnStyle.SetHovered(Brush);
	BtnStyle.SetPressed(Brush);



	return BtnStyle;
}

FString UBPFunctionLibBPLibrary::MD5Encryption(FString SourceStr)
{

	return FMD5::HashAnsiString(*SourceStr);
}

void UBPFunctionLibBPLibrary::OpenExe(FString FilePath)
{
	std::string str_path = TCHAR_TO_UTF8(*FilePath);
	std::wstring wstr_path;
	wstr_path.assign(str_path.begin(), str_path.end());
	ShellExecute(NULL, L"open", wstr_path.c_str(), NULL, NULL, SW_HIDE);
}

void UBPFunctionLibBPLibrary::CloseExe(FString ProcessName)
{
	std::string process = std::string("TASKKILL /F /IM ") + TCHAR_TO_UTF8(*ProcessName);
	system(process.c_str());
}

void UBPFunctionLibBPLibrary::SetComponentAffectDistanceFieldLighting(UPrimitiveComponent* theComponent, bool bIsAffectDistanceFieldLighting)
{
	theComponent->bAffectDistanceFieldLighting = bIsAffectDistanceFieldLighting;
}

void UBPFunctionLibBPLibrary::ReadStringFromText(FString& Result, const FString TextPath)
{
	FFileHelper::LoadFileToString(Result, *TextPath);
}

FString UBPFunctionLibBPLibrary::GetIntranetIp()
{
	FString strLocalIp;
	bool bCanBind = false;
	TSharedRef<FInternetAddr> LocalIp = ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->GetLocalHostAddr(*GLog, bCanBind);
	if (LocalIp->IsValid())
	{
		strLocalIp = LocalIp->ToString(false);
	}

	return strLocalIp;
}

FString UBPFunctionLibBPLibrary::GetLocalHostName()
{
	FString HostName;
	ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->GetHostName(HostName);
	return HostName;
}

FString UBPFunctionLibBPLibrary::GetHardwareID()
{
	return FPlatformMisc::GetLoginId();
}

FString UBPFunctionLibBPLibrary::GetOSName()
{
	FString OSLabel, OSVersion;
	FPlatformMisc::GetOSVersions(OSLabel, OSVersion);
	return FString::Printf(TEXT("%s %s"), *OSLabel, *OSVersion);
}

FString UBPFunctionLibBPLibrary::GetCPUName()
{
	return FPlatformMisc::GetCPUBrand();
}

FString UBPFunctionLibBPLibrary::GetGPUName()
{
	return FPlatformMisc::GetPrimaryGPUBrand();
}

FString UBPFunctionLibBPLibrary::GetOSID()
{
	return FPlatformMisc::GetOperatingSystemId();
}

bool UBPFunctionLibBPLibrary::Is64BitOS()
{
	return FPlatformMisc::Is64bitOperatingSystem();
}

void UBPFunctionLibBPLibrary::GetScreenSize(int32& X, int32& Y)
{
	X = GetSystemMetrics(SM_CXSCREEN);
	Y = GetSystemMetrics(SM_CYSCREEN);
}


void UBPFunctionLibBPLibrary::DoChangeLevelTransform(const ULevelStreaming* StreamingLevel)
{
	check(StreamingLevel);
	ULevel* LoadedLevel = StreamingLevel->GetLoadedLevel();
	if (LoadedLevel != NULL)
	{
		ApplyLevelTransform(LoadedLevel, StreamingLevel->LevelTransform);
	}
}

void UBPFunctionLibBPLibrary::RemoveLevelTransform(const ULevelStreaming* StreamingLevel)
{
	check(StreamingLevel);
	ULevel* LoadedLevel = StreamingLevel->GetLoadedLevel();
	if (LoadedLevel != NULL)
	{
		ApplyLevelTransform(LoadedLevel, StreamingLevel->LevelTransform.Inverse());
	}
}

void UBPFunctionLibBPLibrary::ApplyLevelTransform(ULevel* Level, const FTransform& LevelTransform)
{
	bool bTransformActors = !LevelTransform.Equals(FTransform::Identity);
	if (bTransformActors)
	{
		if (!LevelTransform.GetRotation().IsIdentity())
		{
			// If there is a rotation applied, then the relative precomputed bounds become invalid.
			Level->bTextureStreamingRotationChanged = true;
		}
		// Iterate over all actors in the level and transform them
		for (int32 ActorIndex = 0; ActorIndex < Level->Actors.Num(); ActorIndex++)
		{
			AActor* Actor = Level->Actors[ActorIndex];
			// Don't want to transform children they should stay relative to there parents.
			if (Actor && Actor->GetAttachParentActor() == NULL)
			{
				// Has to modify root component directly as GetActorPosition is incorrect this early
				USceneComponent* RootComponent = Actor->GetRootComponent();
				if (RootComponent)
				{
					TEnumAsByte<EComponentMobility::Type> ComponentMobilityTemp = RootComponent->Mobility;
					RootComponent->Mobility = EComponentMobility::Movable;
					RootComponent->SetRelativeLocationAndRotation(LevelTransform.TransformPosition(RootComponent->GetRelativeLocation()), LevelTransform.TransformRotation(RootComponent->GetRelativeRotation().Quaternion()));
					RootComponent->Mobility = ComponentMobilityTemp;
				}
			}
		}
		Level->OnApplyLevelTransform.Broadcast(LevelTransform);
	}
}

//处理csv
#pragma region CSVFunction

TArray<FString> UBPFunctionLibBPLibrary::LoadCSVFile(const FString& FilePath)
{

	//转换
	//ConvertToUTF8(*FilePath);

	TArray<FString> ResultArray;

	//检测文件是否存在
	if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** File not found: %s ***"), *FilePath);
		return ResultArray;
	}

	//检测读取csv文件是否成功
	TArray<FString> FileContent;
	if (!FFileHelper::LoadFileToStringArray(FileContent, *FilePath))
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Failed to read file: %s ***"), *FilePath);
		return ResultArray;
	}

	// 检查文件至少包含两行（表头 + 数据行）
	if (FileContent.Num() < 2)
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** File is empty or contains only the header: %s ***"), *FilePath);
		return ResultArray;
	}

	// 读取表头，获取 UniqueSymbol 列的位置
	TArray<FString> HeaderColumns;
	// 解析表头
	FileContent[0].ParseIntoArray(HeaderColumns, TEXT(","), true);
	// 获取 UniqueSymbol 列的位置
	int32 UniqueSymbolIndex = HeaderColumns.IndexOfByKey(TEXT("UniqueSymbol"));
	if (UniqueSymbolIndex == INDEX_NONE)
	{
		//CSV表头找不到UniqueSymbol
		//UniqueSymbol需要在csv表格中表头的结尾
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** UniqueSymbol column not found in header: %s ***"), *FilePath);
		return ResultArray;

	}

	// 遍历每一行数据（跳过表头，从第2行开始）
	for (int32 i = 1; i < FileContent.Num(); ++i)
	{
		TArray<FString> Columns;
		FileContent[i].ParseIntoArray(Columns, TEXT(","), true);

		// 确保该行有足够的列
		if (Columns.Num() >= UniqueSymbolIndex + 1)
		{
			// 合并 UniqueSymbol 后的所有列
			FString CombinedLastColumn;
			for (int32 j = UniqueSymbolIndex; j < Columns.Num(); ++j)
			{
				if (!CombinedLastColumn.IsEmpty())
				{
					CombinedLastColumn += TEXT(","); // 添加逗号分隔符
				}
				CombinedLastColumn += Columns[j];
			}

			// 构造合并后的字符串
			FString CombinedString;
			for (int32 j = 0; j < UniqueSymbolIndex; ++j)
			{
				if (!CombinedString.IsEmpty())
				{
					CombinedString += TEXT("*");
				}
				CombinedString += Columns[j];
			}

			// 将 UniqueSymbol 及其后的内容添加到合并后的字符串
			if (!CombinedString.IsEmpty())
			{
				CombinedString += TEXT("*");
			}
			CombinedString += CombinedLastColumn;

			// 将合并后的字符串加入结果数组
			ResultArray.Add(CombinedString);
		}
		else
		{
			// 行格式不正确，记录错误日志
			UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Invalid line format at line %d in file: %s ***"), i + 1, *FilePath);
		}
	}

	return ResultArray;

}

bool UBPFunctionLibBPLibrary::isANSI(const FString& FilePath)
{
	int32 MaxcCheckBytes = 1024; //检查是不是AANSI编码最大长度

	TArray<uint8> FileData;
	if (FFileHelper::LoadFileToArray(FileData, *FilePath))
	{
		//显示检查文件字节的长度
		int32 BytesToCheck = FMath::Min(FileData.Num(), MaxcCheckBytes);

		for (int32 i =0 ; i < BytesToCheck; ++i)
		{
			uint8 Byte = FileData[i];
			// 如果字节大于 0x7F，检查是否符合 UTF-8 字符的字节序列
			if (Byte > 0x7f)
			{
				// 判断 UTF-8 的多字节字符
				if ((Byte & 0xE0) == 0xC0) // 2字节字符的开始字节
				{
					if (i + 1 < BytesToCheck && (FileData[i + 1] & 0xC0) == 0x80)
					{
						++i; // 跳过第二个字节
					}
					else
					{
						UE_LOG(LogTemp, Log, TEXT("BPFunction*** The file is of type ANSI ***"));
						return true; // 如果第二个字节不符合 UTF-8 标准，视为 ANSI 编码
					}
				}
				else if ((Byte & 0xF0) == 0xE0) // 3字节字符的开始字节
				{
					if (i + 2 < BytesToCheck &&
						(FileData[i + 1] & 0xC0) == 0x80 &&
						(FileData[i + 2] & 0xC0) == 0x80)
					{
						i += 2; // 跳过接下来的两个字节
					}
					else
					{
						UE_LOG(LogTemp, Log, TEXT("BPFunction*** The file is of type ANSI ***"));
						return true; // 如果后续字节不符合 UTF-8 标准，视为 ANSI 编码
					}
				}
				else if ((Byte & 0xF8) == 0xF0) // 4字节字符的开始字节
				{
					if (i + 3 < BytesToCheck &&
						(FileData[i + 1] & 0xC0) == 0x80 &&
						(FileData[i + 2] & 0xC0) == 0x80 &&
						(FileData[i + 3] & 0xC0) == 0x80)
					{
						i += 3; // 跳过接下来的三个字节
					}
					else
					{
						UE_LOG(LogTemp, Log, TEXT("BPFunction*** The file is of type ANSI ***"));
						return true; // 如果后续字节不符合 UTF-8 标准，视为 ANSI 编码
					}
				}
				else
				{
					// 如果字节不符合 UTF-8 编码的起始字节序列，认为它是 ANSI 编码
					UE_LOG(LogTemp, Log, TEXT("BPFunction*** The file is of type ANSI ***"));
					return true;
				}
			}

		}

	}
	UE_LOG(LogTemp, Log, TEXT("BPFunction*** The file is of type UTF-8 ***"));
	return false; // 如果没有检测到非法字符，则认为文件是 UTF-8
}



void UBPFunctionLibBPLibrary::CsvBufferToString(FString& Result, const TArray<uint8>& Buffer, const FString& FilePath)
{

  int32 BufferSize = Buffer.Num();
  const ANSICHAR* BufferData = reinterpret_cast<const ANSICHAR*>(Buffer.GetData());

  // 判断文件是否为 ANSI 编码
  if (isANSI(*FilePath))
  {
	  //获得ANSI转换为宽字符所需的长度
	  int32 UnicodeLen = ::MultiByteToWideChar(CP_ACP, 0, BufferData, BufferSize, nullptr, 0);

	  if (UnicodeLen>0)
	  {
		  UE_LOG(LogTemp, Log, TEXT("BPFunction*** UnicodeLen ：：%d ***"), UnicodeLen);
		  TArray<TCHAR>& ResultArray = Result.GetCharArray();
		  ResultArray.Empty();
		  //加一是因为结尾需要一个空字符标记结束
		  ResultArray.AddUninitialized(UnicodeLen + 1);

		  //将ANSI字符转换为Unicode字符
		  int32 WritedTCharLens = ::MultiByteToWideChar(CP_ACP, 0, BufferData, BufferSize,
			  ResultArray.GetData(), UnicodeLen);

		  //成功转换了，才添加字符串结束符
		  if (WritedTCharLens > 0)
		  {
			  // 添加字符串结束符
			  ResultArray[UnicodeLen] = TEXT('\0');
		  }
		  else
		  {
			  // 如果写入失败，清空数组
			  //或者文件就是UTF-8,不需要转换
			  ResultArray.Empty();
		  }
	  }
  }
}




bool UBPFunctionLibBPLibrary::ConvertToUTF8(const FString& FilePath)
{
	//判断文件是否存在
	if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** File does not exist: %s : ***"), *FilePath);
		return false;
	}

	TArray<uint8> RawBuffer;
	//读取文件转为数组
	if (!FFileHelper::LoadFileToArray(RawBuffer, *FilePath))
	{
		//转换失败
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Failed to read file: %s : ***"),*FilePath);
		return false;
	}

	FString FileContent;
	//判断文件编码类别并且转换
	CsvBufferToString(FileContent, RawBuffer, FilePath);

	// 文件为空或者转换后的内容为空，表示读取失败,或者当前格式就是UTF-8
	if (FileContent.IsEmpty())
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** The converted file content is empty or the file format is UTF-8: %s ***"), *FilePath);
		return false;
	}

	// 将文件保存为 UTF-8 格式（不带 BOM）
	if (!FFileHelper::SaveStringToFile(FileContent, *FilePath, FFileHelper::EEncodingOptions::ForceUTF8WithoutBOM))
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Failed to save file as UTF-8: %s ***"), *FilePath);
		return false; // 保存失败
	}

	UE_LOG(LogTemp, Log, TEXT("BPFunction*** File successfully converted to UTF-8: %s ***"), *FilePath);
	return true; // 成功转换

}
#pragma endregion


/*

//加载读取本地Json
#pragma region LoadJson

FString UBPFunctionLibBPLibrary::LoadJsonToString(const FString& FilePath)
{
	//判断路径是否存在
	if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** File not found: %s ***"), *FilePath);
	}
	//解析Json文件
	FString JsonFile;
	FFileHelper::LoadFileToString(JsonFile, *FilePath);
	return JsonFile;
}




bool UBPFunctionLibBPLibrary::ExportInfoToJson(const TArray<FSurroundingAmenities>& SA_Infos)
{

	// 检查输入数组是否为空
	if (SA_Infos.Num() == 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** No surrounding amenities to export"));
		return false;
	}

	TArray<TSharedPtr<FJsonValue>> JsonArray;

	for (const FSurroundingAmenities& Amenity : SA_Infos)
	{

		// 检查 SA_Tags 是否为空
		if (Amenity.SA_Tags.IsEmpty())
		{
			// 如果标签为空，跳过此项
			continue;
		}


		// 使用正确的 UStructToJsonObject 方法
		TSharedPtr<FJsonObject> JsonObject = FJsonObjectConverter::UStructToJsonObject(Amenity);

		if (!JsonObject.IsValid())
		{
			UE_LOG(LogTemp, Error, TEXT("BPFunction*** Failed to convert FSurroundingAmenities to JSON"));
			return false;
		}

		// 将转换后的 JSON 对象添加到数组
		JsonArray.Add(MakeShared<FJsonValueObject>(JsonObject));

	}

	// 序列化 JSON 数组
	FString JsonOutputString;
	TSharedRef<TJsonWriter<>> JsonWriter = TJsonWriterFactory<>::Create(&JsonOutputString);

	if (!FJsonSerializer::Serialize(JsonArray, JsonWriter))
	{
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** Failed to serialize the JSON array"));
		return false;
	}

	// 构建保存路径
	FString SavePath = FPaths::Combine(FPaths::ProjectContentDir(), TEXT("DataStorage"), TEXT("SurroundingAmenities.json"));

	// 保存到文件
	if (!FFileHelper::SaveStringToFile(JsonOutputString, *SavePath))
	{
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** Failed to save JSON to %s"), *SavePath);
		return false;
	}

	UE_LOG(LogTemp, Display, TEXT("BPFunction*** Actors exported successfully to %s"), *SavePath);
	return true;
}



bool UBPFunctionLibBPLibrary::ImportInfoFromJson(TMap<FString, FProcessedSurroundingAmenities>& OutAmenitiesMap)
{
	// 构建加载路径
	FString LoadPath = FPaths::Combine(FPaths::ProjectContentDir(), TEXT("DataStorage"), TEXT("SurroundingAmenities.json"));

	//判断文件是否存在
	FString JsonString;
	if (!FFileHelper::LoadFileToString(JsonString, *LoadPath))
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** File not found: %s ***"), *LoadPath);
		return false;
	}

	//解析json
	TArray<TSharedPtr<FJsonValue>> JsonArray;
	TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(JsonString);
	// 反序列化 JSON 数组
	if (!FJsonSerializer::Deserialize(JsonReader, JsonArray))
	{
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** Failed to deserialize JSON ***"));
		return false;
	}


	//开始添加到键值对中 遍历JsonArray中每一个元素
	for (const TSharedPtr<FJsonValue>& JsonValue : JsonArray)
	{
		// 将 JSON 对象转换为 FSurroundingAmenities
		TSharedPtr<FJsonObject> JsonObject = JsonValue->AsObject();
		if (!JsonObject.IsValid())
		{
			//JsonObject转换失败
			UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Invalid JSON object ***"));

			// 跳过当前循环，继续处理下一个 JsonValue
			continue;
		}

		FProcessedSurroundingAmenities ProcessedAmenity;
        //解析Location
		//FString LocationStr =

		//解析位置
		FString LocationStr = JsonObject->GetStringField(TEXT("SA_Location"));
		FVector ParsedLocation;
		ParsedLocation.InitFromString(LocationStr);
		ProcessedAmenity.PSA_Location = ParsedLocation;

		//解析OpenType
		ProcessedAmenity.PSA_OpenType= JsonObject->GetIntegerField(TEXT("SA_OpenType"));


		//解析锚点
		FString SizeStr = JsonObject->GetStringField(TEXT("SA_InPoivt"));
		FVector2D ParsedSize;
		if (SizeStr.Contains(TEXT("X=")) && SizeStr.Contains(TEXT("Y=")))
		{
			//移除xy
			SizeStr = SizeStr.Replace(TEXT("X="), TEXT("")).Replace(TEXT("Y="), TEXT(""));

			TArray<FString> SizeComponents;
			SizeStr.ParseIntoArray(SizeComponents, TEXT(" "), true);

			if (SizeComponents.Num() == 2)
			{
				ParsedSize = FVector2D(
					FCString::Atof(*SizeComponents[0]),
					FCString::Atof(*SizeComponents[1])
				);
			}

		}
		ProcessedAmenity.PSA_InPoivt = ParsedSize;

		//解析Time
		ProcessedAmenity.PSA_Time = JsonObject->GetNumberField(TEXT("SA_Time"));

		//解析WidgetBrushSizeNum
		ProcessedAmenity.PSA_WidgetBrushSizeNum = JsonObject->GetNumberField(TEXT("SA_WidgetBrushSizeNum"));

		//解析是否使用widget
		ProcessedAmenity.PSA_UsedWidget = JsonObject->GetBoolField(TEXT("SA_UsedWidget"));

		//解析是否可以点击
		ProcessedAmenity.PSA_CanClick = JsonObject->GetBoolField(TEXT("SA_CanClick"));


		//解析Tags
		FString TagsStr = JsonObject->GetStringField(TEXT("SA_Tags"));
		if (!TagsStr.IsEmpty())
		{
			TagsStr.ParseIntoArray(ProcessedAmenity.PSA_Tags, TEXT("*"));
		}


		//添加key
		FString ParsedName = JsonObject->GetStringField(TEXT("SA_Name"));
		OutAmenitiesMap.Add(ParsedName, ProcessedAmenity);

	}

	UE_LOG(LogTemp, Display, TEXT("Successfully imported %d amenities"), OutAmenitiesMap.Num());
	return true;
}

#pragma endregion

*/

void UBPFunctionLibBPLibrary::UpdateLevelTransform(ULevelStreaming* StreamingLevel, const FTransform& Transform)
{

	if (nullptr == StreamingLevel)
	{
		return;
	}
	// Check we are actually changing the value
	if (StreamingLevel->LevelTransform.Equals(Transform))
	{
		return;
	}
	StreamingLevel->Modify();
	// Apply new transform
	RemoveLevelTransform(StreamingLevel);
	StreamingLevel->LevelTransform = Transform;
	DoChangeLevelTransform(StreamingLevel);

}

void UBPFunctionLibBPLibrary::AdjustString(const char* str, int pos, char* buf)
{

	int i = 0, nStart, nEnd;

	nStart = pos;
	while ((str[nStart] == ' '))
		nStart++;

	nEnd = nStart;
	while (str[nEnd])
	{
		buf[i++] = str[nEnd];
		nEnd++;
	}
	buf[i] = '\0';



}

bool UBPFunctionLibBPLibrary::GetMacAddress(int nNetIndex, char* sAddress)
{
	int nIndex = 0;
	char sTmp[10];
	//PIP_ADAPTER_INFO结构体指针存储本机网卡信息
	PIP_ADAPTER_INFO pIpAdapterInfo = new IP_ADAPTER_INFO();
	//得到结构体大小,用于GetAdaptersInfo参数
	unsigned long stSize = sizeof(IP_ADAPTER_INFO);
	//调用GetAdaptersInfo函数,填充pIpAdapterInfo指针变量;其中stSize参数既是一个输入量也是一个输出量
	int nRel = GetAdaptersInfo(pIpAdapterInfo, &stSize);
	if (ERROR_BUFFER_OVERFLOW == nRel)
	{
		//如果函数返回的是ERROR_BUFFER_OVERFLOW
		//则说明GetAdaptersInfo参数传递的内存空间不够,同时其传出stSize,表示需要的空间大小
		//这也是说明为什么stSize既是一个输入量也是一个输出量
		//释放原来的内存空间
		delete pIpAdapterInfo;
		//重新申请内存空间用来存储所有网卡信息
		pIpAdapterInfo = (PIP_ADAPTER_INFO)new BYTE[stSize];
		//再次调用GetAdaptersInfo函数,填充pIpAdapterInfo指针变量
		nRel = GetAdaptersInfo(pIpAdapterInfo, &stSize);
	}
	if (ERROR_SUCCESS == nRel)
	{
		//输出所有网卡信息
		/*//输出网卡信息
		while (pIpAdapterInfo)
		{
			if (nIndex == nNetIndex)
			{
				for (UINT i = 0; i < pIpAdapterInfo->AddressLength; i++)
				{
					sprintf(sTmp, "%02X", pIpAdapterInfo->Address[i]);
					strcat(sAddress, sTmp);
				}
				return TRUE;
			}

			nIndex++;
			pIpAdapterInfo = pIpAdapterInfo->Next;
		}*/

		//输出物理网卡信息
		while (pIpAdapterInfo)
		{

			// 检查是否为物理网卡
			bool isPhysicalAdapter = false;

			if (pIpAdapterInfo->Type == MIB_IF_TYPE_ETHERNET)
			{
				// 检查描述中是否包含虚拟网卡特征
				const char* virtualKeywords[] = {
					"VMware", "VirtualBox", "Hyper-V", "Virtual",
					"TAP", "VPN", "Tunnel", "Pseudo"
				};

				bool isVirtual = false;
				for (const char* keyword : virtualKeywords)
				{
					if (strstr(pIpAdapterInfo->Description, keyword) != NULL)
					{
						isVirtual = true;
						break;
					}
				}

				// 检查MAC地址前缀是否为常见虚拟网卡厂商
				char macPrefix[7] = { 0 };
				sprintf(macPrefix, "%02X%02X%02X",
					pIpAdapterInfo->Address[0],
					pIpAdapterInfo->Address[1],
					pIpAdapterInfo->Address[2]);

				const char* virtualMacPrefixes[] = {
					"000569", // VMware
					"000C29", // VMware
					"001C14", // VMware
					"005056", // VMware
					"080027", // VirtualBox
					"001C42", // Parallels
					"0A0027"  // Microsoft Hyper-V
				};

				bool isVirtualMac = false;
				for (const char* prefix : virtualMacPrefixes)
				{
					if (strcmp(macPrefix, prefix) == 0)
					{
						isVirtualMac = true;
						break;
					}
				}

				// 如果既不是虚拟描述也不是虚拟MAC，则认为是物理网卡
				if (!isVirtual && !isVirtualMac)
				{
					isPhysicalAdapter = true;
				}
			}

			if (isPhysicalAdapter)
			{
				if (nIndex == nNetIndex)
				{
					for (UINT i = 0; i < pIpAdapterInfo->AddressLength; i++)
					{
						sprintf(sTmp, "%02X", pIpAdapterInfo->Address[i]);
						strcat(sAddress, sTmp);
					}
					return TRUE;
				}
				nIndex++;
			}

			pIpAdapterInfo = pIpAdapterInfo->Next;

		}

	}
	//释放内存空间
	if (pIpAdapterInfo)
	{
		delete pIpAdapterInfo;
	}

	return FALSE;


}

ULONG UBPFunctionLibBPLibrary::GetHDSerial(char* pszIDBuff, int nBuffLen, int nDriveID)
{

	//char pszIDBuff[256];
	HANDLE hPhysicalDrive = INVALID_HANDLE_VALUE;
	ULONG ulSerialLen = 0;

	//  Try to get a handle to PhysicalDrive IOCTL, report failure
	//  and exit if can't.
	TCHAR szDriveName[32];
	wsprintf(szDriveName, TEXT("\\\\.\\PhysicalDrive%d"), nDriveID);
	//  Windows NT, Windows 2000, Windows XP - admin rights not required
	hPhysicalDrive = CreateFile(szDriveName, 0,
		FILE_SHARE_READ | FILE_SHARE_WRITE, NULL,
		OPEN_EXISTING, 0, NULL);
	if (hPhysicalDrive == INVALID_HANDLE_VALUE)
	{
		return FALSE;
	}
	STORAGE_PROPERTY_QUERY query;
	DWORD cbBytesReturned = 0;
	static char local_buffer[10000];
	memset((void*)&query, 0, sizeof(query));
	query.PropertyId = StorageDeviceProperty;
	query.QueryType = PropertyStandardQuery;
	memset(local_buffer, 0, sizeof(local_buffer));
	if (DeviceIoControl(hPhysicalDrive, IOCTL_STORAGE_QUERY_PROPERTY,
		&query,
		sizeof(query),
		&local_buffer[0],
		sizeof(local_buffer),
		&cbBytesReturned, NULL))
	{
		STORAGE_DEVICE_DESCRIPTOR* descrip = (STORAGE_DEVICE_DESCRIPTOR*)&local_buffer;
		char serialNumber[1000];
		AdjustString(local_buffer, descrip->SerialNumberOffset, serialNumber);
		if (isalnum(serialNumber[0]))
		{
			ULONG ulSerialLenTemp = (ULONG)strnlen(serialNumber, nBuffLen - 1);
			memcpy(pszIDBuff, serialNumber, ulSerialLenTemp);
			pszIDBuff[ulSerialLenTemp] = NULL;
			ulSerialLen = ulSerialLenTemp;
		}
	}
	if (hPhysicalDrive != INVALID_HANDLE_VALUE)
	{
		CloseHandle(hPhysicalDrive);
	}
	return ulSerialLen;

}

TArray<FString> UBPFunctionLibBPLibrary::GetHDSerial()
{
	TArray<FString> HDSerial;
	HDSerial.Empty();
	char szBuff[256];
	const int MAX_IDE_DRIVES = 16;
	for (int nDriveNum = 0; nDriveNum < MAX_IDE_DRIVES; nDriveNum++)
	{
		ULONG ulLen = GetHDSerial(szBuff, sizeof(szBuff), nDriveNum);
		if (ulLen > 0)
		{
			//_tprintf(TEXT("第%d块硬盘的序列号为：%hs\n"), nDriveNum + 1, szBuff);
			//printf("Disk Serial No.%d: %s\n", nDriveNum + 1, szBuff);
			HDSerial.Add(szBuff);
		}
	}
	UE_LOG(LogTemp, Warning, TEXT("BPFunction***  jiami ***  YunnToHDSerial---%s  ***"), *HDSerial[0]);
	return HDSerial;

}

FString UBPFunctionLibBPLibrary::GetCPUAddresss()
{
	FString cpuAddress;
	INT32 deBuf[4];
	__cpuid(deBuf, 01);
	//printf("CPUID:%.8X%.8X\n", deBuf[0], deBuf[3]);
	//cpuAddress = FString::FromInt(deBuf[0]);
	cpuAddress = FString::Printf(TEXT("%.8X%.8X"), deBuf[0], deBuf[3]);
	//cpuAddress.Append(FString::FromInt(deBuf[3]));
	UE_LOG(LogTemp, Warning, TEXT("BPFunction***  jiami ***   YunnToCPUAddresss---%s  ***"), *cpuAddress);
	return cpuAddress;
}

FString UBPFunctionLibBPLibrary::GetMotherboardSerial()
{

#if PLATFORM_WINDOWS
	BOOL bRet = FALSE;
	HANDLE hReadPipe = NULL;
	HANDLE hWritePipe = NULL;
	PROCESS_INFORMATION pi;
	STARTUPINFOW si;
	SECURITY_ATTRIBUTES sa;

	WCHAR szFetCmd[] = L"wmic BaseBoard get SerialNumber";
	const FString strEnSearch = TEXT("SerialNumber");

	// 初始化缓冲区
	CHAR szBuffer[MAX_COMMAND_SIZE + 1] = { 0 };

	// 初始化结构体
	FMemory::Memzero(&pi, sizeof(pi));
	FMemory::Memzero(&si, sizeof(si));
	FMemory::Memzero(&sa, sizeof(sa));

	pi.hProcess = NULL;
	pi.hThread = NULL;
	si.cb = sizeof(STARTUPINFO);
	sa.nLength = sizeof(SECURITY_ATTRIBUTES);
	sa.lpSecurityDescriptor = NULL;
	sa.bInheritHandle = TRUE;

	// 创建管道
	bRet = CreatePipe(&hReadPipe, &hWritePipe, &sa, 0);
	if (!bRet)
	{
		return TEXT("Warning Failed to create pipe");
	}

	// 设置启动信息
	GetStartupInfoW(&si);
	si.hStdError = hWritePipe;
	si.hStdOutput = hWritePipe;
	si.wShowWindow = SW_HIDE;
	si.dwFlags = STARTF_USESHOWWINDOW | STARTF_USESTDHANDLES;

	// 创建进程
	bRet = CreateProcessW(NULL, szFetCmd, NULL, NULL, TRUE, 0, NULL, NULL, &si, &pi);
	if (!bRet)
	{
		CloseHandle(hWritePipe);
		CloseHandle(hReadPipe);
		return TEXT("Warning Failed to create process");
	}

	// 读取数据
	DWORD count = 0;
	WaitForSingleObject(pi.hProcess, 500);
	bRet = ReadFile(hReadPipe, szBuffer, MAX_COMMAND_SIZE, &count, 0);
	if (!bRet)
	{
		CloseHandle(hWritePipe);
		CloseHandle(hReadPipe);
		CloseHandle(pi.hProcess);
		CloseHandle(pi.hThread);
		return TEXT("Warning Failed to read data");
	}

	// 处理输出结果
	FString strBuffer = UTF8_TO_TCHAR(szBuffer);
	int32 iPos = strBuffer.Find(strEnSearch);

	if (iPos < 0)
	{
		CloseHandle(hWritePipe);
		CloseHandle(hReadPipe);
		CloseHandle(pi.hProcess);
		CloseHandle(pi.hThread);
		return TEXT("Warning Serial number not found");
	}

	// 提取序列号
	FString result = strBuffer.RightChop(iPos + strEnSearch.Len());

	// 清理结果字符串
	result.ReplaceInline(TEXT(" "), TEXT(""));
	result.ReplaceInline(TEXT("\r"), TEXT(""));
	result.ReplaceInline(TEXT("\n"), TEXT(""));
	result = result.TrimStartAndEnd();

	// 清理句柄
	CloseHandle(hWritePipe);
	CloseHandle(hReadPipe);
	CloseHandle(pi.hProcess);
	CloseHandle(pi.hThread);
	UE_LOG(LogTemp, Warning, TEXT("BPFunction***   jiami ***   YunnToMotherboardSerial---%s  ***"), *result);

	return result;
#else
	return TEXT("Warning Platform not supported");
#endif

}

FString UBPFunctionLibBPLibrary::GetYunnToUniqueID()
{

	//cpu + mac + motherboard + HDSerial 用 MD5加密
	FString YunnToUniqueID = FString::Printf(TEXT("%s%s%s%s"), *GetCPUAddresss(), *GetMotherboardSerial(), *GetUniqueMacAddress(), *GetHDSerial()[0]);
	UE_LOG(LogTemp,Warning , TEXT("BPFunction*** YunnToUniqueID---%s  ***"), *YunnToUniqueID);

    return MD5Encryption(YunnToUniqueID);

}

FString UBPFunctionLibBPLibrary::GetUniqueMacAddress()
{
	char sBuf[50] = "";
	// 获取第一个物理网卡的MAC地址 (nNetNum = 0)
	if (GetMacAddress(0, sBuf))
	{
		return FString(sBuf);
	}

	// 如果没有找到任何网卡，返回空字符串
	UE_LOG(LogTemp, Warning, TEXT("BPFunction*** YunnToMacAddress---%s ***"), *FString(sBuf));
	return FString();

}


void UBPFunctionLibBPLibrary::EncryptVideoFile(const FString& FilePath, const FString& DestinationFolderPath, uint8 Key)
{

	// 读取文件内容
	TArray<uint8> FileData;
	if (FFileHelper::LoadFileToArray(FileData, *FilePath))
	{
		// XOR 加密
		for (int32 i = 0; i < 10; ++i)
		{
			FileData[i] ^= Key;
		}

		// 保存加密后的文件
		FFileHelper::SaveArrayToFile(FileData, *DestinationFolderPath);
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("YunnTo:EncryptVideoFile: Failed to load file for encryption"));
	}

}

TArray<FString> UBPFunctionLibBPLibrary::GetMP4FilesInDirectory(const FString& Directory)
{
	TArray<FString> MP4Files;

	// 获取文件夹中的所有文件，过滤MP4文件
	IFileManager& FileManager = IFileManager::Get();
	FString SearchPath = Directory / "*.mp4"; // 使用通配符查找MP4文件
	FileManager.FindFiles(MP4Files, *SearchPath, true, false); // true表示查找文件，false表示不查找子文件夹

	return MP4Files;
}

bool UBPFunctionLibBPLibrary::DeleteDirectory(const FString& Directory)
{
	IFileManager& FileManager = IFileManager::Get();

	// 确保文件夹存在
	if (FileManager.DirectoryExists(*Directory))
	{
		// 删除目录及其内容
		return FileManager.DeleteDirectory(*Directory, true, true); // true表示递归删除目录内容
	}

	// 文件夹不存在
	return false;
}

TArray<FVector2D> UBPFunctionLibBPLibrary::ParseWKT(const FString& WKT)
{
	TArray<FVector2D> Result;

	FString CleanStr = WKT
		// 移除双引号（根据需求可选）
		.Replace(TEXT("\""), TEXT(""))
		// 精确移除前缀
		.Replace(TEXT("MULTIPOLYGON"), TEXT(""), ESearchCase::CaseSensitive)
		// 增强括号清理（包含所有括号层级）
		.Replace(TEXT("((("), TEXT(""))  // 处理起始三重括号
		.Replace(TEXT(")))"), TEXT(""))  // 处理结尾三重括号
		.Replace(TEXT("("), TEXT(""))    // 清理残留单括号
		.Replace(TEXT(")"), TEXT(""))
		// 空格规范化
		.Replace(TEXT("  "), TEXT(" "))
		.TrimStartAndEnd();

	// 分割坐标点
	TArray<FString> Points;
	CleanStr.ParseIntoArray(Points, TEXT(","), true);

	// 遍历处理每个坐标点
	for (const FString& PointStr : Points)
	{
		TArray<FString> Coords;
		PointStr.TrimStartAndEnd().ParseIntoArray(Coords, TEXT(" "), true);

		if (Coords.Num() == 2)
		{
			float Lon = FCString::Atof(*Coords[0]);
			float Lat = FCString::Atof(*Coords[1]);
			Result.Emplace(Lon, Lat);
		}
	}

	return Result;
}

TArray<FPolygonData> UBPFunctionLibBPLibrary::ParseWKT1(const FString& WKT)
{
	TArray<FPolygonData> ResultArray;

	// 移除MULTIPOLYGON前缀，保留括号结构
	FString CleanStr = WKT
		.Replace(TEXT("\""), TEXT(""))
		.Replace(TEXT("MULTIPOLYGON"), TEXT(""), ESearchCase::CaseSensitive)
		.TrimStartAndEnd();

	// 确保至少有外层括号
	if (!CleanStr.StartsWith(TEXT("(")) || !CleanStr.EndsWith(TEXT(")")))
	{
		return ResultArray;
	}

	// 移除最外层括号
	CleanStr = CleanStr.Mid(1, CleanStr.Len() - 2);

	// 分割多个多边形
	TArray<FString> Polygons;
	int32 NestedLevel = 0;
	FString CurrentPolygon;

	// 手动解析多边形，处理嵌套括号
	for (int32 i = 0; i < CleanStr.Len(); ++i)
	{
		TCHAR CurrentChar = CleanStr[i];

		if (CurrentChar == TEXT('('))
		{
			NestedLevel++;
			CurrentPolygon.AppendChar(CurrentChar);
		}
		else if (CurrentChar == TEXT(')'))
		{
			NestedLevel--;
			CurrentPolygon.AppendChar(CurrentChar);

			// 当括号层级回到0且当前多边形不为空时，我们找到了一个完整的多边形
			if (NestedLevel == 0 && !CurrentPolygon.IsEmpty())
			{
				Polygons.Add(CurrentPolygon);
				CurrentPolygon.Empty();

				// 跳过分隔符
				while (i + 1 < CleanStr.Len() && (CleanStr[i + 1] == TEXT(',') || CleanStr[i + 1] == TEXT(' ')))
				{
					i++;
				}
			}
		}
		else
		{
			CurrentPolygon.AppendChar(CurrentChar);
		}
	}

	// 处理每个多边形
	for (int32 PolygonIndex = 0; PolygonIndex < Polygons.Num(); ++PolygonIndex)
	{
		FString PolygonStr = Polygons[PolygonIndex];

		// 移除多边形的外层括号
		if (PolygonStr.StartsWith(TEXT("(")) && PolygonStr.EndsWith(TEXT(")")))
		{
			PolygonStr = PolygonStr.Mid(1, PolygonStr.Len() - 2);
		}

		// 提取坐标点字符串
		FString CoordinatesStr = PolygonStr;
		if (CoordinatesStr.StartsWith(TEXT("(")) && CoordinatesStr.EndsWith(TEXT(")")))
		{
			CoordinatesStr = CoordinatesStr.Mid(1, CoordinatesStr.Len() - 2);
		}

		// 分割坐标点
		TArray<FString> Points;
		CoordinatesStr.ParseIntoArray(Points, TEXT(","), true);

		FPolygonData PolygonData;
		PolygonData.PolygonIndex = PolygonIndex;

		// 处理每个坐标点
		for (const FString& PointStr : Points)
		{
			TArray<FString> Coords;
			PointStr.TrimStartAndEnd().ParseIntoArray(Coords, TEXT(" "), true);

			if (Coords.Num() >= 2)
			{
				float Lon = FCString::Atof(*Coords[0]);
				float Lat = FCString::Atof(*Coords[1]);
				PolygonData.Points.Emplace(Lon, Lat);
			}
		}

		// 将当前多边形数据添加到结果数组中
		if (PolygonData.Points.Num() > 0)
		{
			ResultArray.Add(PolygonData);
		}
	}

	return ResultArray;
}

/*
void UBPFunctionLibBPLibrary::CopyMessageToClipboard(FString text)
{

	FPlatformApplicationMisc::ClipboardCopy(*text);
	//FPlatformMisc::ClipboardCopy(*text);
}

FString UBPFunctionLibBPLibrary::PasteMessageFromClipboard()
{
	FString ClipboardContent;
	FPlatformApplicationMisc::ClipboardPaste(ClipboardContent);
	//FPlatformMisc::ClipboardPaste(ClipboardContent);
	return ClipboardContent;
}

*/


FString UBPFunctionLibBPLibrary::LoadJsonToString(const FString& FilePath)
{
	//判断路径是否存在
	if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** File not found: %s ***"), *FilePath);
	}
	//解析Json文件
	FString JsonFile;
	FFileHelper::LoadFileToString(JsonFile, *FilePath);
	return JsonFile;
}

TArray<FString> UBPFunctionLibBPLibrary::GetPngFilesInDirectory(const FString& Directory)
{
	TArray<FString> FoundFilePaths;

	if (!FPaths::DirectoryExists(Directory))
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** File not found: %s ***"), *Directory);
		return FoundFilePaths; // 返回空数组
	}

	// 获取文件管理器实例
	IFileManager& FileManager = IFileManager::Get();
	FString FileExtension = TEXT("*.png");
	FString SearchPath = FPaths::Combine(Directory, FileExtension);

	TArray<FString> FoundFileNames;
	FileManager.FindFiles(FoundFileNames, *SearchPath, true, false);


	// 遍历找到的文件名，并将它们转换为绝对路径
	for (const FString& FileName : FoundFileNames)
	{
		// 使用FPaths::Combine来正确地组合路径和文件名
		FString FullPath = FPaths::Combine(Directory, FileName);
		FoundFilePaths.Add(FullPath);
	}

	return FoundFilePaths;


}

bool UBPFunctionLibBPLibrary::CallFunctionByName(UObject* Object, const FString& FunctionName, const FString& StringParam)
{

	if (!Object)
	{
		UE_LOG(LogTemp,Error, TEXT("CallFunctionByName: 对象为空，无法执行"));
		return false;
	}

	// 将函数名转换为FName
	FName FuncName(*FunctionName);

	// 内置函数存在性检查 - 查找函数
	UFunction* Function = Object->FindFunction(FuncName);
	if(!Function)
	{
		UE_LOG(LogTemp, Warning, TEXT("CallFunctionByName: 在对象 '%s' 中未找到函数 '%s'，跳过执行"), *Object->GetName(), *FunctionName);
		return false;
	}


	// 检查函数是否可以被调用
	if (!Function->HasAnyFunctionFlags(FUNC_BlueprintCallable))
	{
		UE_LOG(LogTemp, Warning, TEXT("CallFunctionByName: 函数 '%s' 不是蓝图可调用的，但仍尝试执行"), *FunctionName);
	}

	// 移除try-catch，使用Unreal Engine的安全检查方式
	if (Object && Function)
	{
		// 添加额外的安全检查
		if (Object->IsValidLowLevel() && !Object->IsUnreachable())
		{
			// 函数存在且有效，执行函数
			Object->ProcessEvent(Function, (void*)&StringParam);
			UE_LOG(LogTemp, Warning, TEXT("CallFunctionByName: 成功执行函数 '%s'"), *FunctionName);
			return true;
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("CallFunctionByName: 对象无效，无法执行函数 '%s'"), *FunctionName);
			return false;
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("CallFunctionByName: 对象或函数为空，无法执行函数 '%s'"), *FunctionName);
		return false;
	}

}

FString UBPFunctionLibBPLibrary::SaveGameToExpandedJsonString(USaveGame* SaveGameObject)
{
	if (!SaveGameObject)
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** SaveGameObject is null ***"));
		return FString();
	}

	TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);

	// UE5.2 推荐写法，支持新API
	TFunction<void(const UStruct*, const void*, const FString&)> ProcessStruct;
	ProcessStruct = [&](const UStruct* StructDef, const void* StructPtr, const FString& Prefix)
		{
			for (TFieldIterator<FProperty> PropIt(StructDef); PropIt; ++PropIt)
			{
				FProperty* Property = *PropIt;
				if (Property->HasAnyPropertyFlags(CPF_Transient | CPF_DuplicateTransient | CPF_NonPIEDuplicateTransient))
					continue;

				// 字段名路径前缀
				FString FieldPath = Prefix.IsEmpty() ? Property->GetName() : (Prefix + TEXT(".") + Property->GetName());

				// 特殊处理bool类型
				if (FBoolProperty* BoolProp = CastField<FBoolProperty>(Property))
				{
					bool BoolValue = BoolProp->GetPropertyValue_InContainer(StructPtr);
					FString BoolStr = BoolValue ? TEXT("True") : TEXT("False");
					JsonObject->SetStringField(FieldPath, BoolStr);
				}
				// 特殊处理FVector和FRotator
				else if (FStructProperty* StructProp = CastField<FStructProperty>(Property))
				{
					const void* PropValuePtr = Property->ContainerPtrToValuePtr<void>(StructPtr);

					// 特殊处理FVector
					if (StructProp->Struct->GetName() == TEXT("Vector"))
					{
						const FVector* VectorPtr = static_cast<const FVector*>(PropValuePtr);
						FString VectorStr = FString::Printf(TEXT("X=%f Y=%f Z=%f"), VectorPtr->X, VectorPtr->Y, VectorPtr->Z);
						JsonObject->SetStringField(FieldPath, VectorStr);
					}
					// 特殊处理FRotator
					else if (StructProp->Struct->GetName() == TEXT("Rotator"))
					{
						const FRotator* RotatorPtr = static_cast<const FRotator*>(PropValuePtr);
						FString RotatorStr = FString::Printf(TEXT("Pitch=%f Yaw=%f Roll=%f"), RotatorPtr->Pitch, RotatorPtr->Yaw, RotatorPtr->Roll);
						JsonObject->SetStringField(FieldPath, RotatorStr);
					}
					// 其他结构体递归处理
					else
					{
						ProcessStruct(StructProp->Struct, PropValuePtr, FieldPath);
					}
				}
				// 数组
				else if (FArrayProperty* ArrayProp = CastField<FArrayProperty>(Property))
				{
					FScriptArrayHelper Helper(ArrayProp, Property->ContainerPtrToValuePtr<void>(StructPtr));
					for (int32 i = 0; i < Helper.Num(); ++i)
					{
						const void* ElemPtr = Helper.GetRawPtr(i);
						FString ElemPath = FString::Printf(TEXT("%s[%d]"), *FieldPath, i);

						// 数组元素的bool特殊处理
						if (FBoolProperty* ElemBoolProp = CastField<FBoolProperty>(ArrayProp->Inner))
						{
							bool BoolValue = ElemBoolProp->GetPropertyValue(ElemPtr);
							FString BoolStr = BoolValue ? TEXT("True") : TEXT("False");
							JsonObject->SetStringField(ElemPath, BoolStr);
						}
						else if (FStructProperty* InnerStructProp = CastField<FStructProperty>(ArrayProp->Inner))
						{
							// 特殊处理数组中的FVector和FRotator
							if (InnerStructProp->Struct->GetName() == TEXT("Vector"))
							{
								const FVector* VectorPtr = static_cast<const FVector*>(ElemPtr);
								FString VectorStr = FString::Printf(TEXT("X=%f Y=%f Z=%f"), VectorPtr->X, VectorPtr->Y, VectorPtr->Z);
								JsonObject->SetStringField(ElemPath, VectorStr);
							}
							else if (InnerStructProp->Struct->GetName() == TEXT("Rotator"))
							{
								const FRotator* RotatorPtr = static_cast<const FRotator*>(ElemPtr);
								FString RotatorStr = FString::Printf(TEXT("Pitch=%f Yaw=%f Roll=%f"), RotatorPtr->Pitch, RotatorPtr->Yaw, RotatorPtr->Roll);
								JsonObject->SetStringField(ElemPath, RotatorStr);
							}
							else
							{
								ProcessStruct(InnerStructProp->Struct, ElemPtr, ElemPath);
							}
						}
						else
						{
							FString ElemValueStr;
							ArrayProp->Inner->ExportText_Direct(ElemValueStr, Helper.GetRawPtr(i), nullptr, nullptr, PPF_None);
							JsonObject->SetStringField(ElemPath, ElemValueStr);
						}
					}
				}
				// 字典/Map
				else if (FMapProperty* MapProp = CastField<FMapProperty>(Property))
				{
					FScriptMapHelper MapHelper(MapProp, Property->ContainerPtrToValuePtr<void>(StructPtr));
					for (int32 idx = 0; idx < MapHelper.Num(); ++idx)
					{
						if (!MapHelper.IsValidIndex(idx)) continue;

						const void* KeyPtr = MapHelper.GetKeyPtr(idx);
						const void* ValuePtr = MapHelper.GetValuePtr(idx);

						FString KeyStr;
						MapProp->KeyProp->ExportText_Direct(KeyStr, KeyPtr, nullptr, nullptr, PPF_None);
						FString MapFieldPath = FString::Printf(TEXT("%s[%s]"), *FieldPath, *KeyStr);

						// Map值的bool特殊处理
						if (FBoolProperty* MapValBoolProp = CastField<FBoolProperty>(MapProp->ValueProp))
						{
							bool BoolValue = MapValBoolProp->GetPropertyValue(ValuePtr);
							FString BoolStr = BoolValue ? TEXT("True") : TEXT("False");
							JsonObject->SetStringField(MapFieldPath, BoolStr);
						}
						else if (FStructProperty* MapValStruct = CastField<FStructProperty>(MapProp->ValueProp))
						{
							// 特殊处理Map值中的FVector和FRotator
							if (MapValStruct->Struct->GetName() == TEXT("Vector"))
							{
								const FVector* VectorPtr = static_cast<const FVector*>(ValuePtr);
								FString VectorStr = FString::Printf(TEXT("X=%f Y=%f Z=%f"), VectorPtr->X, VectorPtr->Y, VectorPtr->Z);
								JsonObject->SetStringField(MapFieldPath, VectorStr);
							}
							else if (MapValStruct->Struct->GetName() == TEXT("Rotator"))
							{
								const FRotator* RotatorPtr = static_cast<const FRotator*>(ValuePtr);
								FString RotatorStr = FString::Printf(TEXT("Pitch=%f Yaw=%f Roll=%f"), RotatorPtr->Pitch, RotatorPtr->Yaw, RotatorPtr->Roll);
								JsonObject->SetStringField(MapFieldPath, RotatorStr);
							}
							else
							{
								ProcessStruct(MapValStruct->Struct, ValuePtr, MapFieldPath);
							}
						}
						else
						{
							FString ValStr;
							MapProp->ValueProp->ExportText_Direct(ValStr, ValuePtr, nullptr, nullptr, PPF_None);
							JsonObject->SetStringField(MapFieldPath, ValStr);
						}
					}
				}
				// 其它普通类型
				else
				{
					FString ValueStr;
					Property->ExportText_Direct(ValueStr, Property->ContainerPtrToValuePtr<void>(StructPtr), nullptr, nullptr, PPF_None);
					JsonObject->SetStringField(FieldPath, ValueStr);
				}
			}
		};

	// 递归处理SaveGame对象
	ProcessStruct(SaveGameObject->GetClass(), SaveGameObject, TEXT(""));

	// 序列化为字符串（紧凑格式，无换行符）
	FString OutputString;
	TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> Writer = TJsonWriterFactory<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>::Create(&OutputString);
	FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

	return OutputString;

}

bool UBPFunctionLibBPLibrary::RestoreSaveGameFromJsonString(USaveGame* SaveGameObject, const FString& JsonString)
{
	if (!SaveGameObject)
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** SaveGameObject is null ***"));
		return false;
	}

	if (JsonString.IsEmpty())
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** JsonString is empty ***"));
		return false;
	}

	// 首先尝试解析外层JSON（可能是服务器响应格式）
	FString ActualJsonString = JsonString;
	TSharedPtr<FJsonObject> OuterJsonObject;
	TSharedRef<TJsonReader<>> OuterReader = TJsonReaderFactory<>::Create(JsonString);

	if (FJsonSerializer::Deserialize(OuterReader, OuterJsonObject) && OuterJsonObject.IsValid())
	{
		// 检查是否有"data"字段（服务器响应格式）
		if (OuterJsonObject->HasField(TEXT("data")))
		{
			ActualJsonString = OuterJsonObject->GetStringField(TEXT("data"));
			UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Found server response format, extracting data field ***"));
		}
		// 检查是否有"code"字段，确认这是服务器响应
		if (OuterJsonObject->HasField(TEXT("code")))
		{
			int32 Code = OuterJsonObject->GetIntegerField(TEXT("code"));
			UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Server response code: %d ***"), Code);
		}
	}

	// 解析实际的SaveGame JSON数据
	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(ActualJsonString);

	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** Failed to parse SaveGame JSON string ***"));
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** Actual JSON String: %s ***"), *ActualJsonString.Left(500)); // 只显示前500个字符
		return false;
	}

	UE_LOG(LogTemp, Warning, TEXT("BPFunction*** SaveGame JSON parsed successfully, found %d fields ***"), JsonObject->Values.Num());

	// 打印JSON中的所有字段名（用于调试）
	UE_LOG(LogTemp, Warning, TEXT("BPFunction*** JSON Fields: ***"));
	for (const auto& JsonPair : JsonObject->Values)
	{
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Field: %s ***"), *JsonPair.Key);
	}

	// 递归设置属性的函数
	TFunction<bool(const UStruct*, void*, const FString&)> SetStructProperties;
	SetStructProperties = [&](const UStruct* StructDef, void* StructPtr, const FString& Prefix) -> bool
		{
			for (TFieldIterator<FProperty> PropIt(StructDef); PropIt; ++PropIt)
			{
				FProperty* Property = *PropIt;
				if (Property->HasAnyPropertyFlags(CPF_Transient | CPF_DuplicateTransient | CPF_NonPIEDuplicateTransient))
					continue;

				FString PropertyName = Property->GetName();
				UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Checking property: %s, Type: %s ***"), *PropertyName, *Property->GetClass()->GetName());

				// 对于Map属性，需要特殊处理
				if (FMapProperty* MapProp = CastField<FMapProperty>(Property))
				{
					UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Found Map property: %s ***"), *PropertyName);

					void* PropValuePtr = Property->ContainerPtrToValuePtr<void>(StructPtr);
					FScriptMapHelper Helper(MapProp, PropValuePtr);
					Helper.EmptyValues();

					// 查找所有匹配该Map属性的JSON字段
					int32 FoundEntries = 0;
					for (const auto& JsonPair : JsonObject->Values)
					{
						// 检查字段名是否以属性名开头（对于Map类型）
						if (JsonPair.Key.StartsWith(PropertyName + TEXT("[")))
						{
							FString KeyStr;
							if (JsonPair.Key.Split(PropertyName + TEXT("["), nullptr, &KeyStr) &&
								KeyStr.Split(TEXT("]"), &KeyStr, nullptr))
							{
								UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Setting Map entry: %s = %s ***"), *KeyStr, *JsonPair.Value->AsString());

								// 添加新的Map条目
								int32 Index = Helper.AddDefaultValue_Invalid_NeedsRehash();
								void* KeyPtr = Helper.GetKeyPtr(Index);
								void* ValuePtr = Helper.GetValuePtr(Index);

								// 设置Key
								MapProp->KeyProp->ImportText_Direct(*KeyStr, KeyPtr, nullptr, PPF_None);

								// 设置Value
								FString ValStr = JsonPair.Value->AsString();
								UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Setting Map value: %s ***"), *ValStr);

								if (FStructProperty* MapValStruct = CastField<FStructProperty>(MapProp->ValueProp))
								{
									FString StructName = MapValStruct->Struct->GetName();
									UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Map Value Struct Type: %s ***"), *StructName);

									// 检查是否是Vector类型
									if (StructName == TEXT("Vector"))
									{
										FVector* VectorPtr = static_cast<FVector*>(ValuePtr);

										// 解析格式: "X=1.000000 Y=2.000000 Z=3.000000"
										FString XStr, YStr, ZStr;
										if (ValStr.Split(TEXT("X="), nullptr, &XStr) &&
											XStr.Split(TEXT(" Y="), &XStr, &YStr) &&
											YStr.Split(TEXT(" Z="), &YStr, &ZStr))
										{
											VectorPtr->X = FCString::Atof(*XStr);
											VectorPtr->Y = FCString::Atof(*YStr);
											VectorPtr->Z = FCString::Atof(*ZStr);
											UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Parsed Vector: X=%f Y=%f Z=%f ***"), VectorPtr->X, VectorPtr->Y, VectorPtr->Z);
										}
										else
										{
											UE_LOG(LogTemp, Error, TEXT("BPFunction*** Failed to parse Vector: %s ***"), *ValStr);
										}
									}
									// 检查是否是Rotator类型
									else if (StructName == TEXT("Rotator"))
									{
										FRotator* RotatorPtr = static_cast<FRotator*>(ValuePtr);

										// 解析格式: "Pitch=1.000000 Yaw=2.000000 Roll=3.000000"
										FString PitchStr, YawStr, RollStr;
										if (ValStr.Split(TEXT("Pitch="), nullptr, &PitchStr) &&
											PitchStr.Split(TEXT(" Yaw="), &PitchStr, &YawStr) &&
											YawStr.Split(TEXT(" Roll="), &YawStr, &RollStr))
										{
											RotatorPtr->Pitch = FCString::Atof(*PitchStr);
											RotatorPtr->Yaw = FCString::Atof(*YawStr);
											RotatorPtr->Roll = FCString::Atof(*RollStr);
											UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Parsed Rotator: Pitch=%f Yaw=%f Roll=%f ***"), RotatorPtr->Pitch, RotatorPtr->Yaw, RotatorPtr->Roll);
										}
										else
										{
											UE_LOG(LogTemp, Error, TEXT("BPFunction*** Failed to parse Rotator: %s ***"), *ValStr);
										}
									}
									// 其他结构体类型，检查是否包含Vector属性
									else
									{
										UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Using recursive processing for struct: %s ***"), *StructName);

										// 检查结构体中是否有Vector类型的属性，如果有，直接设置
										bool bFoundVectorProperty = false;
										for (TFieldIterator<FProperty> StructPropIt(MapValStruct->Struct); StructPropIt; ++StructPropIt)
										{
											FProperty* StructProperty = *StructPropIt;
											if (FStructProperty* InnerStructProp = CastField<FStructProperty>(StructProperty))
											{
												if (InnerStructProp->Struct->GetName() == TEXT("Vector"))
												{
													// 找到了Vector属性，直接设置
													void* VectorPtr = StructProperty->ContainerPtrToValuePtr<void>(ValuePtr);
													FVector* Vector = static_cast<FVector*>(VectorPtr);

													// 解析Vector字符串
													FString XStr, YStr, ZStr;
													if (ValStr.Split(TEXT("X="), nullptr, &XStr) &&
														XStr.Split(TEXT(" Y="), &XStr, &YStr) &&
														YStr.Split(TEXT(" Z="), &YStr, &ZStr))
													{
														Vector->X = FCString::Atof(*XStr);
														Vector->Y = FCString::Atof(*YStr);
														Vector->Z = FCString::Atof(*ZStr);
														UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Set Vector property %s: X=%f Y=%f Z=%f ***"), *StructProperty->GetName(), Vector->X, Vector->Y, Vector->Z);
														bFoundVectorProperty = true;
														break;
													}
												}
											}
										}

										if (!bFoundVectorProperty)
										{
											// 如果没有找到Vector属性，使用递归处理
											if (!SetStructProperties(MapValStruct->Struct, ValuePtr, JsonPair.Key))
												return false;
										}
									}
								}
								else
								{
									// 普通类型，直接导入
									MapProp->ValueProp->ImportText_Direct(*ValStr, ValuePtr, nullptr, PPF_None);
								}
								FoundEntries++;
							}
						}
					}

					if (FoundEntries > 0)
					{
						Helper.Rehash();
						UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Successfully set %d entries for Map property: %s ***"), FoundEntries, *PropertyName);
					}
					continue;
				}

				// 对于普通属性，检查是否在JSON中存在
				FString FieldPath = Prefix.IsEmpty() ? PropertyName : Prefix + TEXT(".") + PropertyName;
				if (!JsonObject->HasField(FieldPath))
				{
					UE_LOG(LogTemp, Verbose, TEXT("BPFunction*** Field not found in JSON: %s ***"), *FieldPath);
					continue;
				}

				UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Processing field: %s, Property Type: %s ***"), *FieldPath, *Property->GetClass()->GetName());

				void* PropValuePtr = Property->ContainerPtrToValuePtr<void>(StructPtr);

				// 处理结构体属性
				if (FStructProperty* StructProp = CastField<FStructProperty>(Property))
				{
					// 特殊处理FVector
					if (StructProp->Struct->GetName() == TEXT("Vector"))
					{
						FString VectorStr = JsonObject->GetStringField(FieldPath);
						FVector* VectorPtr = static_cast<FVector*>(PropValuePtr);

						// 解析格式: "X=1.000000 Y=2.000000 Z=3.000000"
						FString XStr, YStr, ZStr;
						if (VectorStr.Split(TEXT("X="), nullptr, &XStr) &&
							XStr.Split(TEXT(" Y="), &XStr, &YStr) &&
							YStr.Split(TEXT(" Z="), &YStr, &ZStr))
						{
							VectorPtr->X = FCString::Atof(*XStr);
							VectorPtr->Y = FCString::Atof(*YStr);
							VectorPtr->Z = FCString::Atof(*ZStr);
						}
					}
					// 特殊处理FRotator
					else if (StructProp->Struct->GetName() == TEXT("Rotator"))
					{
						FString RotatorStr = JsonObject->GetStringField(FieldPath);
						FRotator* RotatorPtr = static_cast<FRotator*>(PropValuePtr);

						// 解析格式: "Pitch=1.000000 Yaw=2.000000 Roll=3.000000"
						FString PitchStr, YawStr, RollStr;
						if (RotatorStr.Split(TEXT("Pitch="), nullptr, &PitchStr) &&
							PitchStr.Split(TEXT(" Yaw="), &PitchStr, &YawStr) &&
							YawStr.Split(TEXT(" Roll="), &YawStr, &RollStr))
						{
							RotatorPtr->Pitch = FCString::Atof(*PitchStr);
							RotatorPtr->Yaw = FCString::Atof(*YawStr);
							RotatorPtr->Roll = FCString::Atof(*RollStr);
						}
					}
					// 其他结构体递归处理
					else
					{
						if (!SetStructProperties(StructProp->Struct, PropValuePtr, FieldPath))
							return false;
					}
				}
				// 处理数组属性
				else if (FArrayProperty* ArrayProp = CastField<FArrayProperty>(Property))
				{
					FScriptArrayHelper Helper(ArrayProp, PropValuePtr);

					// 首先清空数组
					Helper.EmptyValues();

					// 查找所有以该字段路径开头的数组元素
					int32 MaxIndex = -1;
					for (const auto& JsonPair : JsonObject->Values)
					{
						if (JsonPair.Key.StartsWith(FieldPath + TEXT("[")))
						{
							FString IndexStr;
							if (JsonPair.Key.Split(TEXT("["), nullptr, &IndexStr) &&
								IndexStr.Split(TEXT("]"), &IndexStr, nullptr))
							{
								int32 Index = FCString::Atoi(*IndexStr);
								MaxIndex = FMath::Max(MaxIndex, Index);
							}
						}
					}

					// 如果找到了数组元素，重新调整数组大小
					if (MaxIndex >= 0)
					{
						Helper.Resize(MaxIndex + 1);

						for (int32 i = 0; i <= MaxIndex; ++i)
						{
							FString ElemPath = FString::Printf(TEXT("%s[%d]"), *FieldPath, i);

							if (JsonObject->HasField(ElemPath))
							{
								void* ElemPtr = Helper.GetRawPtr(i);

								if (FStructProperty* InnerStructProp = CastField<FStructProperty>(ArrayProp->Inner))
								{
									if (!SetStructProperties(InnerStructProp->Struct, ElemPtr, ElemPath))
										return false;
								}
								else
								{
									FString ElemValueStr = JsonObject->GetStringField(ElemPath);
									ArrayProp->Inner->ImportText_Direct(*ElemValueStr, ElemPtr, nullptr, PPF_None);
								}
							}
						}
					}
				}

				// 其它普通类型
				else
				{
					FString ValueStr = JsonObject->GetStringField(FieldPath);
					Property->ImportText_Direct(*ValueStr, PropValuePtr, nullptr, PPF_None);
				}
			}
			return true;
		};

	// 递归处理SaveGame对象
	bool bSuccess = SetStructProperties(SaveGameObject->GetClass(), SaveGameObject, TEXT(""));

	if (bSuccess)
	{
		// 标记SaveGame对象为已修改，确保更改能被保存
		SaveGameObject->MarkPackageDirty();
		UE_LOG(LogTemp, Log, TEXT("BPFunction*** Successfully restored SaveGame from JSON ***"));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** Failed to restore SaveGame from JSON ***"));
	}

	return bSuccess;
}

bool UBPFunctionLibBPLibrary::TestRestoreSaveGameFromFile(USaveGame* SaveGameObject, const FString& JsonFilePath)
{
	if (!SaveGameObject)
	{
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** SaveGameObject is null ***"));
		return false;
	}

	// 读取JSON文件
	FString JsonString;
	if (!FFileHelper::LoadFileToString(JsonString, *JsonFilePath))
	{
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** Failed to load file: %s ***"), *JsonFilePath);
		return false;
	}

	UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Loaded JSON file: %s ***"), *JsonFilePath);
	UE_LOG(LogTemp, Warning, TEXT("BPFunction*** JSON content length: %d ***"), JsonString.Len());

	// 调用恢复函数
	return RestoreSaveGameFromJsonString(SaveGameObject, JsonString);
}

bool UBPFunctionLibBPLibrary::RestoreSaveGameFromServerResponse(USaveGame* SaveGameObject, const FString& ServerResponseString)
{
	if (!SaveGameObject)
	{
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** SaveGameObject is null ***"));
		return false;
	}

	if (ServerResponseString.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** ServerResponseString is empty ***"));
		return false;
	}

	// 解析服务器响应
	TSharedPtr<FJsonObject> ServerJsonObject;
	TSharedRef<TJsonReader<>> ServerReader = TJsonReaderFactory<>::Create(ServerResponseString);

	if (!FJsonSerializer::Deserialize(ServerReader, ServerJsonObject) || !ServerJsonObject.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** Failed to parse server response ***"));
		return false;
	}

	// 检查响应码
	if (ServerJsonObject->HasField(TEXT("code")))
	{
		int32 Code = ServerJsonObject->GetIntegerField(TEXT("code"));
		if (Code != 0)
		{
			UE_LOG(LogTemp, Error, TEXT("BPFunction*** Server returned error code: %d ***"), Code);
			return false;
		}
	}

	// 提取data字段
	if (!ServerJsonObject->HasField(TEXT("data")))
	{
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** Server response missing 'data' field ***"));
		return false;
	}

	FString SaveGameJsonString = ServerJsonObject->GetStringField(TEXT("data"));
	UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Extracted SaveGame JSON from server response ***"));

	// 调用原始的恢复函数
	return RestoreSaveGameFromJsonString(SaveGameObject, SaveGameJsonString);
}

void UBPFunctionLibBPLibrary::DebugPrintSaveGameProperties(USaveGame* SaveGameObject)
{
	if (!SaveGameObject)
	{
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** SaveGameObject is null ***"));
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Debugging SaveGame properties for class: %s ***"), *SaveGameObject->GetClass()->GetName());

	for (TFieldIterator<FProperty> PropIt(SaveGameObject->GetClass()); PropIt; ++PropIt)
	{
		FProperty* Property = *PropIt;
		if (Property->HasAnyPropertyFlags(CPF_Transient | CPF_DuplicateTransient | CPF_NonPIEDuplicateTransient))
			continue;

		FString PropertyName = Property->GetName();
		FString PropertyType = Property->GetClass()->GetName();

		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Property: %s, Type: %s ***"), *PropertyName, *PropertyType);

		// 对于Map属性，显示详细信息
		if (FMapProperty* MapProp = CastField<FMapProperty>(Property))
		{
			void* PropValuePtr = Property->ContainerPtrToValuePtr<void>(SaveGameObject);
			FScriptMapHelper Helper(MapProp, PropValuePtr);

			UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Map property %s has %d entries ***"), *PropertyName, Helper.Num());

			for (int32 i = 0; i < Helper.Num(); ++i)
			{
				if (Helper.IsValidIndex(i))
				{
					void* KeyPtr = Helper.GetKeyPtr(i);
					void* ValuePtr = Helper.GetValuePtr(i);

					FString KeyStr;
					FString ValueStr;

					MapProp->KeyProp->ExportText_Direct(KeyStr, KeyPtr, nullptr, nullptr, PPF_None);
					MapProp->ValueProp->ExportText_Direct(ValueStr, ValuePtr, nullptr, nullptr, PPF_None);

					UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Map[%s] = %s ***"), *KeyStr, *ValueStr);
				}
			}
		}
		// 对于普通属性，显示值
		else
		{
			void* PropValuePtr = Property->ContainerPtrToValuePtr<void>(SaveGameObject);
			FString ValueStr;
			Property->ExportText_Direct(ValueStr, PropValuePtr, nullptr, nullptr, PPF_None);
			UE_LOG(LogTemp, Warning, TEXT("BPFunction*** %s = %s ***"), *PropertyName, *ValueStr);
		}
	}
}

FVector UBPFunctionLibBPLibrary::TestParseVectorString(const FString& VectorString)
{
	UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Testing Vector parsing for: %s ***"), *VectorString);

	FVector Result = FVector::ZeroVector;

	// 解析格式: "X=1.000000 Y=2.000000 Z=3.000000"
	FString XStr, YStr, ZStr;
	if (VectorString.Split(TEXT("X="), nullptr, &XStr) &&
		XStr.Split(TEXT(" Y="), &XStr, &YStr) &&
		YStr.Split(TEXT(" Z="), &YStr, &ZStr))
	{
		Result.X = FCString::Atof(*XStr);
		Result.Y = FCString::Atof(*YStr);
		Result.Z = FCString::Atof(*ZStr);
		UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Parsed Vector: X=%f Y=%f Z=%f ***"), Result.X, Result.Y, Result.Z);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** Failed to parse Vector: %s ***"), *VectorString);
	}

	return Result;
}

